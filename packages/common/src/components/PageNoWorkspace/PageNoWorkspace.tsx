import React from 'react';
import { Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { navigateToUrl } from 'single-spa';
import CompanyLogo from '../../images/glide-big-logo.png';
import NotFoundImg from '../../images/notfound-404.svg';
import './styles.css';

const PageNoWorkspace = () => {
    return (
        <div className="no-workspace-root">
            <Typography variant="h1">No Workspace</Typography>
        </div>
        // <div
        //     id="page-not-found"
        //     className="not-found"
        //     style={{
        //         zIndex: 1203,
        //         width: '100%',
        //         height: '100%',
        //         display: 'flex',
        //         position: 'fixed',
        //         top: 0,
        //         left: 0,
        //         bottom: 0,
        //         justifyContent: 'center',
        //         alignItems: 'center',
        //         flexDirection: 'column',
        //         backgroundSize: 'cover',
        //         backgroundColor: '#212427',
        //         overflowY: 'scroll',
        //     }}
        // >
        //     <img className="logo" src={CompanyLogo} alt="GLIDESYSML PLATFORM" />
        //     <img className="logo" src={NotFoundImg} alt="Page not found" />
        //     <Typography
        //         variant="h1"
        //         style={{
        //             fontSize: '36px',
        //             color: '#DBDFEA',
        //             fontWeight: 400,
        //             marginBottom: '1em',
        //             marginTop: '2em',
        //         }}
        //     >
        //         Page not found
        //     </Typography>
        //     <Typography
        //         style={{
        //             fontSize: '16px',
        //             color: '#DBDFEA',
        //             fontWeight: 400,
        //             maxWidth: '396px',
        //             textAlign: 'center',
        //             lineHeight: '1.5em',
        //         }}
        //     >
        //         We looked everywhere for this page.
        //     </Typography>
        //     <Typography
        //         style={{
        //             fontSize: '16px',
        //             color: '#DBDFEA',
        //             fontWeight: 400,
        //             maxWidth: '396px',
        //             textAlign: 'center',
        //             lineHeight: '1.5em',
        //         }}
        //     >
        //         Are you sure the URL is correct?
        //     </Typography>
        //     <button
        //         id="page-not-found-back-button"
        //         style={{
        //             backgroundColor: 'transparent',
        //             color: '#DBDFEA',
        //             fontSize: '14px',
        //             textDecoration: 'none',
        //             border: '1px solid #FFFFFF',
        //             borderRadius: '8px',
        //             padding: '10px 25px',
        //             marginTop: '40px',
        //             cursor: 'pointer',
        //             marginBottom: '4em',
        //         }}
        //     >
        //         Back to Dashboard
        //     </button>
        // </div>
    );
};

export default PageNoWorkspace;
